// types/quiz.ts

export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'matching';
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  tags: string[];
  reference?: {
    book: string;
    chapter: number;
    verse?: number;
  };
  points: number;
}

export interface Quiz {
  id: string;
  title: string;
  slug: string;
  description: string;
  category: 'testament' | 'book' | 'character' | 'theme' | 'chapter';
  difficulty: 'easy' | 'medium' | 'hard';
  questions: QuizQuestion[];
  timeLimit?: number; // in minutes
  passingScore: number; // percentage
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    totalQuestions: number;
    estimatedTime: number;
    topics: string[];
    bibleBooks: string[];
  };
}

export interface QuizAttempt {
  id: string;
  quizId: string;
  userId?: string;
  sessionId: string;
  answers: QuizAnswer[];
  score: number;
  percentage: number;
  timeSpent: number; // in seconds
  completed: boolean;
  startedAt: Date;
  completedAt?: Date;
}

export interface QuizAnswer {
  questionId: string;
  userAnswer: string | string[];
  isCorrect: boolean;
  timeSpent: number; // in seconds
  pointsEarned: number;
}

export interface QuizResult {
  attempt: QuizAttempt;
  quiz: Quiz;
  summary: {
    totalQuestions: number;
    correctAnswers: number;
    incorrectAnswers: number;
    skippedAnswers: number;
    finalScore: number;
    percentage: number;
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    passed: boolean;
  };
  categoryBreakdown: {
    [category: string]: {
      total: number;
      correct: number;
      percentage: number;
    };
  };
  recommendations: {
    studyTopics: string[];
    relatedQuizzes: string[];
    bibleReadings: string[];
  };
}

export interface QuizStats {
  totalAttempts: number;
  averageScore: number;
  bestScore: number;
  completionRate: number;
  averageTime: number;
  difficultyDistribution: {
    easy: number;
    medium: number;
    hard: number;
  };
  popularCategories: {
    category: string;
    count: number;
  }[];
}

export interface UserProgress {
  userId: string;
  totalQuizzesTaken: number;
  totalQuestionsAnswered: number;
  averageScore: number;
  bestScore: number;
  streakDays: number;
  lastActivityDate: Date;
  achievements: Achievement[];
  categoryProgress: {
    [category: string]: {
      quizzesTaken: number;
      averageScore: number;
      mastery: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    };
  };
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'score' | 'streak' | 'completion' | 'knowledge' | 'special';
  requirement: string;
  unlockedAt: Date;
  points: number;
}
