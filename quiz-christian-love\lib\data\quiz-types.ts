// lib/data/quiz-types.ts

export interface QuizType {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: 'testament' | 'book' | 'character' | 'theme' | 'chapter';
}

export const QUIZ_TYPES: QuizType[] = [
  {
    id: 'old-testament',
    name: 'Old Testament Quizzes',
    slug: 'old-testament',
    description: 'Test your knowledge of the Old Testament books, stories, and characters',
    icon: 'scroll',
    difficulty: 'medium',
    category: 'testament'
  },
  {
    id: 'new-testament',
    name: 'New Testament Quizzes',
    slug: 'new-testament',
    description: 'Explore the life of <PERSON> and the early church through interactive quizzes',
    icon: 'cross',
    difficulty: 'medium',
    category: 'testament'
  },
  {
    id: 'gospels',
    name: 'Gospel Quizzes',
    slug: 'gospels',
    description: 'Focus on <PERSON>, <PERSON>, <PERSON>, and <PERSON> - the life and teachings of <PERSON>',
    icon: 'heart',
    difficulty: 'easy',
    category: 'book'
  },
  {
    id: 'proverbs-psalms',
    name: 'Proverbs & Psalms Quizzes',
    slug: 'proverbs-psalms',
    description: 'Wisdom literature and songs of worship from the Bible',
    icon: 'star',
    difficulty: 'medium',
    category: 'book'
  },
  {
    id: 'paul-character',
    name: 'Paul Character Quiz',
    slug: 'paul-character',
    description: 'Test your knowledge about the Apostle Paul\'s life and ministry',
    icon: 'user',
    difficulty: 'hard',
    category: 'character'
  },
  {
    id: 'moses-character',
    name: 'Moses Character Quiz',
    slug: 'moses-character',
    description: 'Learn about Moses, the great leader and lawgiver of Israel',
    icon: 'user',
    difficulty: 'medium',
    category: 'character'
  },
  {
    id: 'david-character',
    name: 'David Character Quiz',
    slug: 'david-character',
    description: 'Explore the life of King David, the man after God\'s own heart',
    icon: 'user',
    difficulty: 'medium',
    category: 'character'
  },
  {
    id: 'faith-theme',
    name: 'Faith Thematic Quiz',
    slug: 'faith-theme',
    description: 'Biblical teachings and examples of faith throughout Scripture',
    icon: 'shield',
    difficulty: 'medium',
    category: 'theme'
  },
  {
    id: 'prayer-theme',
    name: 'Prayer Thematic Quiz',
    slug: 'prayer-theme',
    description: 'Learn about prayer through biblical examples and teachings',
    icon: 'hands',
    difficulty: 'easy',
    category: 'theme'
  },
  {
    id: 'salvation-theme',
    name: 'Salvation Thematic Quiz',
    slug: 'salvation-theme',
    description: 'Understanding God\'s plan of salvation through Scripture',
    icon: 'cross',
    difficulty: 'hard',
    category: 'theme'
  },
  {
    id: 'genesis-1',
    name: 'Genesis 1 Chapter Quiz',
    slug: 'genesis-1',
    description: 'The creation account in Genesis chapter 1',
    icon: 'globe',
    difficulty: 'easy',
    category: 'chapter'
  },
  {
    id: 'romans-8',
    name: 'Romans 8 Chapter Quiz',
    slug: 'romans-8',
    description: 'Life in the Spirit - Romans chapter 8',
    icon: 'dove',
    difficulty: 'hard',
    category: 'chapter'
  },
  {
    id: 'john-3',
    name: 'John 3 Chapter Quiz',
    slug: 'john-3',
    description: 'Jesus and Nicodemus - John chapter 3',
    icon: 'heart',
    difficulty: 'medium',
    category: 'chapter'
  }
];

export const DIFFICULTY_LEVELS = ['easy', 'medium', 'hard'] as const;
export const QUIZ_CATEGORIES = ['testament', 'book', 'character', 'theme', 'chapter'] as const;

export function getQuizTypeBySlug(slug: string): QuizType | undefined {
  return QUIZ_TYPES.find(quiz => quiz.slug === slug);
}

export function getQuizTypesByCategory(category: string): QuizType[] {
  return QUIZ_TYPES.filter(quiz => quiz.category === category);
}

export function getQuizTypesByDifficulty(difficulty: string): QuizType[] {
  return QUIZ_TYPES.filter(quiz => quiz.difficulty === difficulty);
}
