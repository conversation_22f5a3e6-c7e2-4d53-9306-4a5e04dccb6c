// app/layout.tsx
import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { generateWebsiteSchema, generateOrganizationSchema, injectSchema } from "@/lib/utils/schema";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: {
    default: "Quiz Christian Love - Bible Quiz & Study",
    template: "%s | Quiz Christian Love"
  },
  description: "Test your Bible knowledge with interactive quizzes covering all 66 books, characters, and themes. Grow in faith through Scripture study and Christian love.",
  keywords: ["Bible quiz", "Christian quiz", "Scripture study", "Bible knowledge", "Christian education", "Bible trivia", "faith quiz"],
  authors: [{ name: "Quiz Christian Love" }],
  creator: "Quiz Christian Love",
  publisher: "Quiz Christian Love",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.SITE_URL || 'https://quiz-christian-love.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Quiz Christian Love - Bible Quiz & Study',
    description: 'Test your Bible knowledge with interactive quizzes covering all 66 books, characters, and themes.',
    siteName: 'Quiz Christian Love',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Quiz Christian Love - Bible Quiz & Study',
    description: 'Test your Bible knowledge with interactive quizzes covering all 66 books, characters, and themes.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
