// types/bible.ts

export interface BibleVerse {
  id: string;
  book: string;
  chapter: number;
  verse: number;
  text: string;
  version: string;
}

export interface BibleChapter {
  book: string;
  chapter: number;
  verses: BibleVerse[];
  summary?: string;
  keyThemes?: string[];
}

export interface BibleBook {
  id: string;
  name: string;
  slug: string;
  testament: 'old' | 'new';
  chapters: number;
  category: string;
  description: string;
  author?: string;
  writtenDate?: string;
  keyThemes?: string[];
}

export interface BibleSearchResult {
  verse: BibleVerse;
  relevanceScore: number;
  context: {
    previousVerse?: BibleVerse;
    nextVerse?: BibleVerse;
  };
}

export interface BibleReference {
  book: string;
  chapter: number;
  verse?: number;
  endVerse?: number;
  text: string;
}

export interface StudyNote {
  id: string;
  reference: BibleReference;
  title: string;
  content: string;
  category: 'historical' | 'theological' | 'practical' | 'cross-reference';
  tags: string[];
}

export interface BibleCharacter {
  id: string;
  name: string;
  slug: string;
  description: string;
  keyVerses: BibleReference[];
  books: string[];
  significance: string;
  lifeEvents: string[];
}

export interface BibleTheme {
  id: string;
  name: string;
  slug: string;
  description: string;
  keyVerses: BibleReference[];
  relatedTopics: string[];
  difficulty: 'easy' | 'medium' | 'hard';
}
