// app/bible/[book]/[chapter]/page.tsx
import { Metadata } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getBibleBookBySlug } from '@/lib/data/bible-books';
import { generateChapterSEO } from '@/lib/utils/seo';
import { getRandomTemplate, generateMetaFromTemplate } from '@/lib/data/meta-templates';

interface Props {
  params: {
    book: string;
    chapter: string;
  };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const book = getBibleBookBySlug(params.book);
  const chapterNum = parseInt(params.chapter);
  
  if (!book || isNaN(chapterNum) || chapterNum < 1 || chapterNum > book.chapters) {
    return {
      title: 'Chapter Not Found',
      description: 'The requested Bible chapter could not be found.'
    };
  }
  
  const template = getRandomTemplate('chapter');
  const { title, description } = generateMetaFromTemplate(template, {
    book: book.name,
    chapter: params.chapter
  });
  
  const keywords = [
    `${book.name} ${chapterNum}`,
    `${book.name} chapter ${chapterNum}`,
    `${book.name} ${chapterNum} quiz`,
    'Bible chapter study',
    'Scripture quiz',
    'Bible study',
    'Christian education'
  ];
  
  return {
    title,
    description,
    keywords: keywords.join(', '),
    alternates: {
      canonical: `/bible/${params.book}/${params.chapter}`
    }
  };
}

export default async function BibleChapterPage({ params }: Props) {
  const book = getBibleBookBySlug(params.book);
  const chapterNum = parseInt(params.chapter);
  
  if (!book || isNaN(chapterNum) || chapterNum < 1 || chapterNum > book.chapters) {
    notFound();
  }

  // Mock chapter data - in a real app, this would come from the Bible API
  const mockVerses = [
    { verse: 1, text: `${book.name} ${chapterNum}:1 - This is the first verse of the chapter.` },
    { verse: 2, text: `${book.name} ${chapterNum}:2 - This is the second verse of the chapter.` },
    { verse: 3, text: `${book.name} ${chapterNum}:3 - This is the third verse of the chapter.` },
    { verse: 4, text: `${book.name} ${chapterNum}:4 - This is the fourth verse of the chapter.` },
    { verse: 5, text: `${book.name} ${chapterNum}:5 - This is the fifth verse of the chapter.` }
  ];

  const navigation = {
    previousChapter: chapterNum > 1 ? chapterNum - 1 : null,
    nextChapter: chapterNum < book.chapters ? chapterNum + 1 : null
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="mb-4">
              <ol className="flex space-x-2 text-blue-200">
                <li><Link href="/bible" className="hover:text-white">Bible</Link></li>
                <li>›</li>
                <li><Link href={`/bible/${book.slug}`} className="hover:text-white">{book.name}</Link></li>
                <li>›</li>
                <li className="text-white">Chapter {chapterNum}</li>
              </ol>
            </nav>
            
            <h1 className="text-4xl font-bold mb-2">{book.name} Chapter {chapterNum}</h1>
            <p className="text-xl text-blue-100">{book.description}</p>
            
            <div className="flex flex-wrap gap-4 mt-6">
              <span className="bg-blue-700 px-3 py-1 rounded-full text-sm">
                {book.testament === 'old' ? 'Old Testament' : 'New Testament'}
              </span>
              <span className="bg-blue-700 px-3 py-1 rounded-full text-sm">
                {book.category}
              </span>
              <span className="bg-blue-700 px-3 py-1 rounded-full text-sm">
                Chapter {chapterNum} of {book.chapters}
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Chapter Content */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-4 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-3">
                <div className="bg-white rounded-lg shadow-md p-8">
                  <h2 className="text-2xl font-bold text-gray-800 mb-6">Chapter Text</h2>
                  
                  <div className="space-y-4">
                    {mockVerses.map((verse) => (
                      <div key={verse.verse} className="flex">
                        <span className="text-blue-600 font-semibold mr-4 min-w-[2rem]">
                          {verse.verse}
                        </span>
                        <p className="text-gray-700 leading-relaxed">{verse.text}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Note:</strong> This is a demo version. In the full application, 
                      the complete chapter text would be loaded from a Bible API service.
                    </p>
                  </div>
                </div>

                {/* Chapter Navigation */}
                <div className="mt-8 flex justify-between">
                  {navigation.previousChapter ? (
                    <Link
                      href={`/bible/${book.slug}/${navigation.previousChapter}`}
                      className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      ← Chapter {navigation.previousChapter}
                    </Link>
                  ) : (
                    <div></div>
                  )}
                  
                  {navigation.nextChapter ? (
                    <Link
                      href={`/bible/${book.slug}/${navigation.nextChapter}`}
                      className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Chapter {navigation.nextChapter} →
                    </Link>
                  ) : (
                    <div></div>
                  )}
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="space-y-6">
                  {/* Quick Actions */}
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Study Tools</h3>
                    <div className="space-y-3">
                      <Link
                        href={`/quizzes/${book.slug}-${chapterNum}`}
                        className="block w-full bg-green-600 text-white text-center py-2 rounded-lg hover:bg-green-700 transition-colors"
                      >
                        Take Chapter Quiz
                      </Link>
                      <Link
                        href={`/bible/${book.slug}`}
                        className="block w-full bg-blue-600 text-white text-center py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        All {book.name} Chapters
                      </Link>
                      <Link
                        href="/bible"
                        className="block w-full border-2 border-gray-300 text-gray-700 text-center py-2 rounded-lg hover:border-gray-400 transition-colors"
                      >
                        All Bible Books
                      </Link>
                    </div>
                  </div>

                  {/* Chapter Info */}
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Chapter Info</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Book:</span>
                        <span className="font-medium">{book.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Chapter:</span>
                        <span className="font-medium">{chapterNum}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Testament:</span>
                        <span className="font-medium">{book.testament === 'old' ? 'Old' : 'New'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Category:</span>
                        <span className="font-medium">{book.category}</span>
                      </div>
                    </div>
                  </div>

                  {/* Key Themes */}
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Key Themes</h3>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                        Faith
                      </span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                        God's Word
                      </span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                        Christian Living
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Content */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-800 mb-8">Continue Your Study</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <Link
                href={`/quizzes/${book.slug}`}
                className="bg-green-50 border border-green-200 rounded-lg p-6 hover:bg-green-100 transition-colors"
              >
                <h3 className="text-lg font-semibold text-green-800 mb-2">Book Quiz</h3>
                <p className="text-green-600 text-sm">Test your knowledge of the entire book</p>
              </Link>
              
              <Link
                href="/topics"
                className="bg-purple-50 border border-purple-200 rounded-lg p-6 hover:bg-purple-100 transition-colors"
              >
                <h3 className="text-lg font-semibold text-purple-800 mb-2">Bible Topics</h3>
                <p className="text-purple-600 text-sm">Explore biblical themes and teachings</p>
              </Link>
              
              <Link
                href="/quizzes"
                className="bg-blue-50 border border-blue-200 rounded-lg p-6 hover:bg-blue-100 transition-colors"
              >
                <h3 className="text-lg font-semibold text-blue-800 mb-2">More Quizzes</h3>
                <p className="text-blue-600 text-sm">Browse all available Bible quizzes</p>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
