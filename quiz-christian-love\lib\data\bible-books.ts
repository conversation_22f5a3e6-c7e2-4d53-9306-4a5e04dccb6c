// lib/data/bible-books.ts

export interface BibleBook {
  id: string;
  name: string;
  slug: string;
  testament: 'old' | 'new';
  chapters: number;
  category: string;
  description: string;
}

export const OLD_TESTAMENT_BOOKS: BibleBook[] = [
  { id: '01', name: '<PERSON>', slug: 'genesis', testament: 'old', chapters: 50, category: 'Law', description: 'The book of beginnings' },
  { id: '02', name: 'Exodus', slug: 'exodus', testament: 'old', chapters: 40, category: 'Law', description: 'The deliverance from Egypt' },
  { id: '03', name: 'Levi<PERSON>', slug: 'leviticus', testament: 'old', chapters: 27, category: 'Law', description: 'Laws for holy living' },
  { id: '04', name: 'Numbers', slug: 'numbers', testament: 'old', chapters: 36, category: 'Law', description: 'Wilderness wanderings' },
  { id: '05', name: 'Deuteronomy', slug: 'deuteronomy', testament: 'old', chapters: 34, category: 'Law', description: '<PERSON>\' final words' },
  { id: '06', name: '<PERSON>', slug: 'joshua', testament: 'old', chapters: 24, category: 'History', description: 'Conquest of the Promised Land' },
  { id: '07', name: 'Judges', slug: 'judges', testament: 'old', chapters: 21, category: 'History', description: 'Israel\'s cycle of sin and deliverance' },
  { id: '08', name: 'Ruth', slug: 'ruth', testament: 'old', chapters: 4, category: 'History', description: 'A story of loyalty and love' },
  { id: '09', name: '1 Samuel', slug: '1-samuel', testament: 'old', chapters: 31, category: 'History', description: 'Samuel, Saul, and David' },
  { id: '10', name: '2 Samuel', slug: '2-samuel', testament: 'old', chapters: 24, category: 'History', description: 'David\'s reign' },
  { id: '11', name: '1 Kings', slug: '1-kings', testament: 'old', chapters: 22, category: 'History', description: 'Solomon and the divided kingdom' },
  { id: '12', name: '2 Kings', slug: '2-kings', testament: 'old', chapters: 25, category: 'History', description: 'The fall of Israel and Judah' },
  { id: '13', name: '1 Chronicles', slug: '1-chronicles', testament: 'old', chapters: 29, category: 'History', description: 'David\'s lineage and reign' },
  { id: '14', name: '2 Chronicles', slug: '2-chronicles', testament: 'old', chapters: 36, category: 'History', description: 'Judah\'s kings and temple' },
  { id: '15', name: 'Ezra', slug: 'ezra', testament: 'old', chapters: 10, category: 'History', description: 'Return from exile' },
  { id: '16', name: 'Nehemiah', slug: 'nehemiah', testament: 'old', chapters: 13, category: 'History', description: 'Rebuilding Jerusalem\'s walls' },
  { id: '17', name: 'Esther', slug: 'esther', testament: 'old', chapters: 10, category: 'History', description: 'God\'s providence through Esther' },
  { id: '18', name: 'Job', slug: 'job', testament: 'old', chapters: 42, category: 'Wisdom', description: 'Suffering and God\'s sovereignty' },
  { id: '19', name: 'Psalms', slug: 'psalms', testament: 'old', chapters: 150, category: 'Wisdom', description: 'Songs of worship and praise' },
  { id: '20', name: 'Proverbs', slug: 'proverbs', testament: 'old', chapters: 31, category: 'Wisdom', description: 'Practical wisdom for living' },
  { id: '21', name: 'Ecclesiastes', slug: 'ecclesiastes', testament: 'old', chapters: 12, category: 'Wisdom', description: 'The meaning of life' },
  { id: '22', name: 'Song of Solomon', slug: 'song-of-solomon', testament: 'old', chapters: 8, category: 'Wisdom', description: 'Love and devotion' },
  { id: '23', name: 'Isaiah', slug: 'isaiah', testament: 'old', chapters: 66, category: 'Major Prophets', description: 'The Messiah and God\'s salvation' },
  { id: '24', name: 'Jeremiah', slug: 'jeremiah', testament: 'old', chapters: 52, category: 'Major Prophets', description: 'The weeping prophet' },
  { id: '25', name: 'Lamentations', slug: 'lamentations', testament: 'old', chapters: 5, category: 'Major Prophets', description: 'Mourning for Jerusalem' },
  { id: '26', name: 'Ezekiel', slug: 'ezekiel', testament: 'old', chapters: 48, category: 'Major Prophets', description: 'Visions and restoration' },
  { id: '27', name: 'Daniel', slug: 'daniel', testament: 'old', chapters: 12, category: 'Major Prophets', description: 'Faithfulness in exile' }
];

export const NEW_TESTAMENT_BOOKS: BibleBook[] = [
  { id: '40', name: 'Matthew', slug: 'matthew', testament: 'new', chapters: 28, category: 'Gospels', description: 'Jesus as the promised Messiah' },
  { id: '41', name: 'Mark', slug: 'mark', testament: 'new', chapters: 16, category: 'Gospels', description: 'Jesus as the suffering servant' },
  { id: '42', name: 'Luke', slug: 'luke', testament: 'new', chapters: 24, category: 'Gospels', description: 'Jesus as the Son of Man' },
  { id: '43', name: 'John', slug: 'john', testament: 'new', chapters: 21, category: 'Gospels', description: 'Jesus as the Son of God' },
  { id: '44', name: 'Acts', slug: 'acts', testament: 'new', chapters: 28, category: 'History', description: 'The early church' },
  { id: '45', name: 'Romans', slug: 'romans', testament: 'new', chapters: 16, category: 'Paul\'s Letters', description: 'Salvation by faith' },
  { id: '46', name: '1 Corinthians', slug: '1-corinthians', testament: 'new', chapters: 16, category: 'Paul\'s Letters', description: 'Church unity and love' },
  { id: '47', name: '2 Corinthians', slug: '2-corinthians', testament: 'new', chapters: 13, category: 'Paul\'s Letters', description: 'Paul\'s ministry defense' },
  { id: '48', name: 'Galatians', slug: 'galatians', testament: 'new', chapters: 6, category: 'Paul\'s Letters', description: 'Freedom in Christ' },
  { id: '49', name: 'Ephesians', slug: 'ephesians', testament: 'new', chapters: 6, category: 'Paul\'s Letters', description: 'Unity in the body of Christ' },
  { id: '50', name: 'Philippians', slug: 'philippians', testament: 'new', chapters: 4, category: 'Paul\'s Letters', description: 'Joy in Christ' },
  { id: '51', name: 'Colossians', slug: 'colossians', testament: 'new', chapters: 4, category: 'Paul\'s Letters', description: 'Christ\'s supremacy' },
  { id: '52', name: '1 Thessalonians', slug: '1-thessalonians', testament: 'new', chapters: 5, category: 'Paul\'s Letters', description: 'Christ\'s return' },
  { id: '53', name: '2 Thessalonians', slug: '2-thessalonians', testament: 'new', chapters: 3, category: 'Paul\'s Letters', description: 'The Day of the Lord' }
];

export const ALL_BIBLE_BOOKS = [...OLD_TESTAMENT_BOOKS, ...NEW_TESTAMENT_BOOKS];

export function getBibleBookBySlug(slug: string): BibleBook | undefined {
  return ALL_BIBLE_BOOKS.find(book => book.slug === slug);
}

export function getBibleBooksByTestament(testament: 'old' | 'new'): BibleBook[] {
  return ALL_BIBLE_BOOKS.filter(book => book.testament === testament);
}

export function getBibleBooksByCategory(category: string): BibleBook[] {
  return ALL_BIBLE_BOOKS.filter(book => book.category === category);
}
