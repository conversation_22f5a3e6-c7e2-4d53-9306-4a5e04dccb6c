// lib/data/topics.ts

export interface Topic {
  id: string;
  name: string;
  slug: string;
  description: string;
  category: 'teaching' | 'character' | 'event' | 'doctrine' | 'prophecy';
  difficulty: 'easy' | 'medium' | 'hard';
  relatedBooks: string[];
  keyVerses: string[];
}

export const BIBLICAL_TOPICS: Topic[] = [
  {
    id: 'jesus-parables',
    name: 'Jesus\' Parables',
    slug: 'jesus-parables',
    description: 'Stories Jesus told to teach spiritual truths',
    category: 'teaching',
    difficulty: 'medium',
    relatedBooks: ['matthew', 'mark', 'luke'],
    keyVerses: ['Matthew 13:3', 'Luke 15:3', 'Mark 4:2']
  },
  {
    id: 'ten-commandments',
    name: 'Ten Commandments',
    slug: 'ten-commandments',
    description: 'God\'s moral law given to <PERSON> on Mount Sinai',
    category: 'doctrine',
    difficulty: 'easy',
    relatedBooks: ['exodus', 'deuteronomy'],
    keyVerses: ['Exodus 20:1-17', 'Deuteronomy 5:4-21']
  },
  {
    id: 'end-times',
    name: 'End Times',
    slug: 'end-times',
    description: 'Biblical prophecies about the last days and Christ\'s return',
    category: 'prophecy',
    difficulty: 'hard',
    relatedBooks: ['daniel', 'matthew', 'revelation'],
    keyVerses: ['Matthew 24:36', 'Revelation 22:20', 'Daniel 12:1']
  },
  {
    id: 'spiritual-gifts',
    name: 'Spiritual Gifts',
    slug: 'spiritual-gifts',
    description: 'Gifts given by the Holy Spirit to believers',
    category: 'doctrine',
    difficulty: 'medium',
    relatedBooks: ['1-corinthians', 'romans', 'ephesians'],
    keyVerses: ['1 Corinthians 12:4-11', 'Romans 12:6-8', 'Ephesians 4:11-13']
  },
  {
    id: 'sermon-on-mount',
    name: 'Sermon on the Mount',
    slug: 'sermon-on-mount',
    description: 'Jesus\' famous teaching in Matthew 5-7',
    category: 'teaching',
    difficulty: 'medium',
    relatedBooks: ['matthew'],
    keyVerses: ['Matthew 5:3-12', 'Matthew 6:9-13', 'Matthew 7:12']
  },
  {
    id: 'fruits-of-spirit',
    name: 'Fruits of the Spirit',
    slug: 'fruits-of-spirit',
    description: 'Character qualities produced by the Holy Spirit',
    category: 'doctrine',
    difficulty: 'easy',
    relatedBooks: ['galatians'],
    keyVerses: ['Galatians 5:22-23']
  },
  {
    id: 'armor-of-god',
    name: 'Armor of God',
    slug: 'armor-of-god',
    description: 'Spiritual protection for believers',
    category: 'doctrine',
    difficulty: 'medium',
    relatedBooks: ['ephesians'],
    keyVerses: ['Ephesians 6:10-18']
  },
  {
    id: 'creation-account',
    name: 'Creation Account',
    slug: 'creation-account',
    description: 'God\'s creation of the world in Genesis',
    category: 'event',
    difficulty: 'easy',
    relatedBooks: ['genesis'],
    keyVerses: ['Genesis 1:1', 'Genesis 1:27', 'Genesis 2:7']
  },
  {
    id: 'exodus-from-egypt',
    name: 'Exodus from Egypt',
    slug: 'exodus-from-egypt',
    description: 'Israel\'s deliverance from Egyptian slavery',
    category: 'event',
    difficulty: 'medium',
    relatedBooks: ['exodus'],
    keyVerses: ['Exodus 12:31', 'Exodus 14:21', 'Exodus 15:1']
  },
  {
    id: 'crucifixion-resurrection',
    name: 'Crucifixion and Resurrection',
    slug: 'crucifixion-resurrection',
    description: 'Jesus\' death and resurrection for our salvation',
    category: 'event',
    difficulty: 'medium',
    relatedBooks: ['matthew', 'mark', 'luke', 'john'],
    keyVerses: ['John 19:30', 'Matthew 28:6', 'Luke 24:6']
  },
  {
    id: 'great-commission',
    name: 'Great Commission',
    slug: 'great-commission',
    description: 'Jesus\' command to make disciples of all nations',
    category: 'teaching',
    difficulty: 'easy',
    relatedBooks: ['matthew', 'mark', 'acts'],
    keyVerses: ['Matthew 28:19-20', 'Mark 16:15', 'Acts 1:8']
  },
  {
    id: 'love-commandments',
    name: 'Love Commandments',
    slug: 'love-commandments',
    description: 'Jesus\' teachings on love for God and others',
    category: 'teaching',
    difficulty: 'easy',
    relatedBooks: ['matthew', 'mark', 'luke', '1-john'],
    keyVerses: ['Matthew 22:37-39', 'John 13:34-35', '1 John 4:7-8']
  }
];

export function getTopicBySlug(slug: string): Topic | undefined {
  return BIBLICAL_TOPICS.find(topic => topic.slug === slug);
}

export function getTopicsByCategory(category: string): Topic[] {
  return BIBLICAL_TOPICS.filter(topic => topic.category === category);
}

export function getTopicsByDifficulty(difficulty: string): Topic[] {
  return BIBLICAL_TOPICS.filter(topic => topic.difficulty === difficulty);
}

export function getTopicsByBook(bookSlug: string): Topic[] {
  return BIBLICAL_TOPICS.filter(topic => topic.relatedBooks.includes(bookSlug));
}
